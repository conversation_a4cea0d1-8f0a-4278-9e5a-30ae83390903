import{a as B}from"./chunk-FE3NIZ6B.js";import"./chunk-WPPT3EJF.js";import{B as i,C as L,Da as _,Db as D,F as v,G as r,Hb as E,I as g,Ib as F,K as o,L as s,M as d,Q as x,R as O,Tb as N,Xb as A,Y as a,Yb as j,_ as R,_b as z,fc as H,ib as $,j as m,m as y,ma as S,n as p,na as b,pa as I,vb as W,wb as w,z as C}from"./chunk-E442IOFQ.js";import"./chunk-LR6AIEJQ.js";import"./chunk-6NVMNNPA.js";import"./chunk-KW2BML7M.js";import"./chunk-SV2ZKNWA.js";import"./chunk-YJDO75HI.js";import"./chunk-HC6MZPB3.js";import"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-ZJ5IMUT4.js";import"./chunk-SGSBBWFA.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-EI2QJP5N.js";import"./chunk-W6U2AR23.js";import"./chunk-APL3YEA6.js";import"./chunk-HSXX7Y3C.js";import"./chunk-FUGLTCJS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import{g as T}from"./chunk-2R6CW7ES.js";function K(n,u){if(n&1&&(o(0,"div",14)(1,"h2"),a(2,"\u{1F4CA} Test Results"),s(),o(3,"div",15)(4,"div",16),d(5,"ion-icon",17),o(6,"span"),a(7,"API Connection"),s()(),o(8,"div",16),d(9,"ion-icon",17),o(10,"span"),a(11,"Route Calculation"),s()(),o(12,"div",16),d(13,"ion-icon",17),o(14,"span"),a(15,"Walking Mode"),s()(),o(16,"div",16),d(17,"ion-icon",17),o(18,"span"),a(19,"Cycling Mode"),s()(),o(20,"div",16),d(21,"ion-icon",17),o(22,"span"),a(23,"Driving Mode"),s()()()()),n&2){let e=O();i(4),g("success",e.testResults.connectionWorks)("error",e.testResults.connectionWorks===!1),i(),r("name",e.testResults.connectionWorks?"checkmark-circle":"close-circle"),i(3),g("success",e.testResults.routingWorks)("error",e.testResults.routingWorks===!1),i(),r("name",e.testResults.routingWorks?"checkmark-circle":"close-circle"),i(3),g("success",e.testResults["foot-walkingWorks"])("error",e.testResults["foot-walkingWorks"]===!1),i(),r("name",e.testResults["foot-walkingWorks"]?"checkmark-circle":"close-circle"),i(3),g("success",e.testResults["cycling-regularWorks"])("error",e.testResults["cycling-regularWorks"]===!1),i(),r("name",e.testResults["cycling-regularWorks"]?"checkmark-circle":"close-circle"),i(3),g("success",e.testResults["driving-carWorks"])("error",e.testResults["driving-carWorks"]===!1),i(),r("name",e.testResults["driving-carWorks"]?"checkmark-circle":"close-circle")}}function Q(n,u){if(n&1&&d(0,"div",21),n&2){let e=u.$implicit;r("innerHTML",e,C)}}function U(n,u){if(n&1&&(o(0,"div",18)(1,"h2"),a(2,"\u{1F4DD} Test Logs"),s(),o(3,"div",19),v(4,Q,1,1,"div",20),s()()),n&2){let e=O();i(4),r("ngForOf",e.logs)}}function V(n,u){n&1&&(o(0,"div",22),d(1,"ion-spinner",23),o(2,"p"),a(3,"Running tests..."),s()())}var q=(()=>{class n{constructor(e){this.orsService=e,this.testResults={},this.logs=[],this.isLoading=!1}hasTestResults(){return Object.keys(this.testResults).length>0}ngOnInit(){this.addLog("\u{1F680} OpenRouteService Test Page Initialized")}addLog(e){let c=new Date().toLocaleTimeString();this.logs.push(`[${c}] ${e}`),console.log(e)}runTests(){return T(this,null,function*(){this.isLoading=!0,this.logs=[],this.testResults={},this.addLog("\u{1F9EA} Starting OpenRouteService Tests..."),this.addLog("\u{1F50C} Test 1: Testing API connection...");try{let t=yield this.orsService.testConnection();this.testResults.connectionWorks=t.success,t.success?(this.addLog("\u2705 API connection successful!"),this.addLog(`\u{1F4CA} Details: ${JSON.stringify(t.details)}`)):(this.addLog("\u274C API connection failed!"),this.addLog(`\u2757 Error: ${t.message}`),t.details&&this.addLog(`\u{1F4CA} Details: ${JSON.stringify(t.details)}`))}catch(t){this.testResults.connectionWorks=!1,this.addLog(`\u274C Connection test error: ${t.message||t}`)}this.addLog("\u{1F5FA}\uFE0F Test 2: Testing route calculation...");try{this.addLog(`\u{1F4CD} Testing route from [${10.3157}, ${123.8854}] to [${10.3257}, ${123.8954}]`);let h=yield this.orsService.getDirections(123.8854,10.3157,123.8954,10.3257,"foot-walking",{geometries:"geojson",overview:"simplified"});if(h.routes&&h.routes.length>0){this.testResults.routingWorks=!0;let M=h.routes[0],k=this.orsService.getRouteSummary(M);this.addLog("\u2705 Route calculation successful!"),this.addLog(`\u{1F4CF} Distance: ${k.distanceText}`),this.addLog(`\u23F1\uFE0F Duration: ${k.durationText}`),this.addLog(`\u{1F6E3}\uFE0F Route coordinates: ${M.geometry.coordinates.length} points`)}else this.testResults.routingWorks=!1,this.addLog("\u274C No routes returned from OpenRouteService")}catch(t){this.testResults.routingWorks=!1,this.addLog(`\u274C Route calculation failed: ${t.message||t}`)}this.addLog("\u{1F6B6}\u200D\u2642\uFE0F\u{1F6B4}\u200D\u2642\uFE0F\u{1F697} Test 3: Testing different travel modes...");let e=["foot-walking","cycling-regular","driving-car"];for(let t of e)try{this.addLog(`Testing ${t} mode...`);let l=yield this.orsService.getDirections(123.8854,10.3157,123.8954,10.3257,t);if(l.routes&&l.routes.length>0){let P=l.routes[0],f=this.orsService.getRouteSummary(P);this.addLog(`\u2705 ${t}: ${f.distanceText}, ${f.durationText}`),this.testResults[`${t}Works`]=!0}else this.addLog(`\u274C ${t}: No route found`),this.testResults[`${t}Works`]=!1}catch(l){this.addLog(`\u274C ${t}: ${l.message||l}`),this.testResults[`${t}Works`]=!1}this.addLog("\u{1F504} Test 4: Testing profile conversion..."),["walking","cycling","driving","foot","bicycle","car"].forEach(t=>{let l=this.orsService.convertTravelModeToProfile(t);this.addLog(`${t} \u2192 ${l}`)}),this.addLog("\u{1F3C1} All tests completed!"),this.isLoading=!1})}clearLogs(){this.logs=[],this.testResults={}}static{this.\u0275fac=function(c){return new(c||n)(L(B))}}static{this.\u0275cmp=y({type:n,selectors:[["app-ors-test"]],decls:23,vars:8,consts:[[3,"translucent"],["slot","start"],["defaultHref","/"],[3,"fullscreen"],[1,"container"],[1,"header-section"],[1,"controls-section"],["expand","block","color","primary",3,"click","disabled"],["name","play","slot","start"],["expand","block","fill","outline","color","medium",3,"click","disabled"],["name","trash","slot","start"],["class","results-section",4,"ngIf"],["class","logs-section",4,"ngIf"],["class","loading-section",4,"ngIf"],[1,"results-section"],[1,"result-grid"],[1,"result-item"],[3,"name"],[1,"logs-section"],[1,"logs-container"],["class","log-entry",3,"innerHTML",4,"ngFor","ngForOf"],[1,"log-entry",3,"innerHTML"],[1,"loading-section"],["name","crescent"]],template:function(c,t){c&1&&(o(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-title"),a(3,"OpenRouteService Test"),s(),o(4,"ion-buttons",1),d(5,"ion-back-button",2),s()()(),o(6,"ion-content",3)(7,"div",4)(8,"div",5)(9,"h1"),a(10,"\u{1F5FA}\uFE0F OpenRouteService API Test"),s(),o(11,"p"),a(12,"Test the OpenRouteService integration and routing functionality"),s()(),o(13,"div",6)(14,"ion-button",7),x("click",function(){return t.runTests()}),d(15,"ion-icon",8),a(16),s(),o(17,"ion-button",9),x("click",function(){return t.clearLogs()}),d(18,"ion-icon",10),a(19," Clear Logs "),s()(),v(20,K,24,25,"div",11)(21,U,5,1,"div",12)(22,V,4,0,"div",13),s()()),c&2&&(r("translucent",!0),i(6),r("fullscreen",!0),i(8),r("disabled",t.isLoading),i(2),R(" ",t.isLoading?"Running Tests...":"Run All Tests"," "),i(),r("disabled",t.isLoading),i(3),r("ngIf",t.hasTestResults()),i(),r("ngIf",t.logs.length>0),i(),r("ngIf",t.isLoading))},dependencies:[S,b,W,w,D,E,F,N,A,j,z],styles:[".container[_ngcontent-%COMP%]{padding:20px;max-width:800px;margin:0 auto}.header-section[_ngcontent-%COMP%]{text-align:center;margin-bottom:30px}.header-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:var(--ion-color-primary);margin-bottom:10px}.header-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:16px}.controls-section[_ngcontent-%COMP%]{margin-bottom:30px}.controls-section[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-bottom:10px}.results-section[_ngcontent-%COMP%]{margin-bottom:30px}.results-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:var(--ion-color-primary);margin-bottom:15px}.result-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:15px;margin-bottom:20px}.result-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:15px;border-radius:8px;background:var(--ion-color-light);border:2px solid transparent}.result-item.success[_ngcontent-%COMP%]{background:var(--ion-color-success-tint);border-color:var(--ion-color-success);color:var(--ion-color-success-shade)}.result-item.success[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:var(--ion-color-success)}.result-item.error[_ngcontent-%COMP%]{background:var(--ion-color-danger-tint);border-color:var(--ion-color-danger);color:var(--ion-color-danger-shade)}.result-item.error[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:var(--ion-color-danger)}.result-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-right:10px}.result-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500}.logs-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:var(--ion-color-primary);margin-bottom:15px}.logs-container[_ngcontent-%COMP%]{background:var(--ion-color-dark);color:var(--ion-color-light);border-radius:8px;padding:15px;max-height:400px;overflow-y:auto;font-family:Courier New,monospace;font-size:12px;line-height:1.4}.log-entry[_ngcontent-%COMP%]{margin-bottom:5px;word-wrap:break-word}.log-entry[_ngcontent-%COMP%]:last-child{margin-bottom:0}.loading-section[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.loading-section[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%]{margin-bottom:20px}.loading-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:16px}@media (max-width: 768px){.container[_ngcontent-%COMP%]{padding:15px}.result-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.logs-container[_ngcontent-%COMP%]{font-size:11px;max-height:300px}}"]})}}return n})();var X=[{path:"",component:q}],G=(()=>{class n{static{this.\u0275fac=function(c){return new(c||n)}}static{this.\u0275mod=p({type:n})}static{this.\u0275inj=m({imports:[_.forChild(X),_]})}}return n})();var ut=(()=>{class n{static{this.\u0275fac=function(c){return new(c||n)}}static{this.\u0275mod=p({type:n})}static{this.\u0275inj=m({imports:[I,$,H,G]})}}return n})();export{ut as OrsTestPageModule};
