import{a as ot}from"./chunk-KUBQHLD5.js";import{a as nt,c as rt}from"./chunk-7IBF2Q3E.js";import{a as it}from"./chunk-JCIM63K2.js";import{b as tt}from"./chunk-I4SN7ED3.js";import{a as et}from"./chunk-FE3NIZ6B.js";import"./chunk-3J7GGTVR.js";import{a as Z}from"./chunk-WPPT3EJF.js";import"./chunk-2LL5MXLB.js";import{B as s,Db as G,F as P,G as h,Hb as J,I as y,Ib as K,K as i,L as a,M as f,P as T,Q as C,R as d,Y as g,Yb as Y,Z as v,_ as I,ac as q,cc as H,d as E,da as S,ec as Q,fc as X,ib as U,l as x,m as L,ma as z,na as A,pa as $,r as w,ra as D,s as b,vb as W,wa as V,wb as j,ya as B}from"./chunk-E442IOFQ.js";import"./chunk-LR6AIEJQ.js";import"./chunk-6NVMNNPA.js";import"./chunk-KW2BML7M.js";import"./chunk-SV2ZKNWA.js";import"./chunk-YJDO75HI.js";import"./chunk-HC6MZPB3.js";import"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-ZJ5IMUT4.js";import"./chunk-SGSBBWFA.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-EI2QJP5N.js";import"./chunk-W6U2AR23.js";import"./chunk-APL3YEA6.js";import"./chunk-HSXX7Y3C.js";import"./chunk-FUGLTCJS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import{a as N,b as R,f as at,g as _}from"./chunk-2R6CW7ES.js";var c=at(rt());function st(r,M){if(r&1){let t=T();i(0,"app-real-time-navigation",26),C("routeUpdated",function(e){w(t);let o=d();return b(o.onNavigationRouteUpdated(e))})("navigationStopped",function(){w(t);let e=d();return b(e.onNavigationStopped())}),a()}if(r&2){let t=d();h("destination",t.navigationDestination)("travelMode",t.selectedTransportMode==="walking"?"foot-walking":t.selectedTransportMode==="cycling"?"cycling-regular":"driving-car")("autoStart",t.isRealTimeNavigationActive)}}function ct(r,M){if(r&1&&(i(0,"span",35),g(1),a()),r&2){let t=d().$implicit,n=d();s(),I(" \u{1F4CD} ",n.calculateDistanceInKm(t)," km away ")}}function lt(r,M){if(r&1){let t=T();i(0,"div",27),C("click",function(){let e=w(t).$implicit,o=d();return b(o.selectCenterFromList(e))}),i(1,"div",28)(2,"h4"),g(3),a(),i(4,"p",29),g(5),a(),i(6,"div",30),P(7,ct,2,1,"span",31),i(8,"span",32),g(9),a()()(),i(10,"div",33),f(11,"ion-icon",34),a()()}if(r&2){let t=M.$implicit,n=d();s(3),v(t.name),s(2),v(t.address),s(2),h("ngIf",n.userLocation),s(2),I("\u{1F465} ",t.capacity||"N/A"," capacity")}}function gt(r,M){if(r&1&&(i(0,"div",28)(1,"h3"),g(2),a(),i(3,"p"),g(4),a()()),r&2){let t=d();s(2),v(t.selectedCenter.name),s(2),v(t.selectedCenter.address)}}function pt(r,M){if(r&1&&(i(0,"div",46)(1,"span",47),g(2),a(),i(3,"span",35),g(4),a()()),r&2){let t=d(2);s(2),v(t.formatTime(t.routeInfo.walking==null?null:t.routeInfo.walking.duration)),s(2),v(t.formatDistance(t.routeInfo.walking==null?null:t.routeInfo.walking.distance))}}function dt(r,M){if(r&1&&(i(0,"div",46)(1,"span",47),g(2),a(),i(3,"span",35),g(4),a()()),r&2){let t=d(2);s(2),v(t.formatTime(t.routeInfo.cycling==null?null:t.routeInfo.cycling.duration)),s(2),v(t.formatDistance(t.routeInfo.cycling==null?null:t.routeInfo.cycling.distance))}}function ut(r,M){if(r&1&&(i(0,"div",46)(1,"span",47),g(2),a(),i(3,"span",35),g(4),a()()),r&2){let t=d(2);s(2),v(t.formatTime(t.routeInfo.driving==null?null:t.routeInfo.driving.duration)),s(2),v(t.formatDistance(t.routeInfo.driving==null?null:t.routeInfo.driving.distance))}}function mt(r,M){if(r&1){let t=T();i(0,"button",48),C("click",function(){w(t);let e=d(2);return b(e.startRealTimeNavigation(e.selectedCenter))}),f(1,"ion-icon",49),i(2,"span"),g(3,"Start Real-time Navigation"),a()()}}function ht(r,M){if(r&1){let t=T();i(0,"div",36)(1,"div",37),f(2,"ion-icon",38),i(3,"span"),g(4,"Choose Transportation"),a()(),i(5,"div",39)(6,"button",40),C("click",function(){w(t);let e=d();return b(e.selectTransportMode("walking"))}),f(7,"ion-icon",41),i(8,"span"),g(9,"Walk"),a(),P(10,pt,5,2,"div",42),a(),i(11,"button",40),C("click",function(){w(t);let e=d();return b(e.selectTransportMode("cycling"))}),f(12,"ion-icon",43),i(13,"span"),g(14,"Cycle"),a(),P(15,dt,5,2,"div",42),a(),i(16,"button",40),C("click",function(){w(t);let e=d();return b(e.selectTransportMode("driving"))}),f(17,"ion-icon",44),i(18,"span"),g(19,"Drive"),a(),P(20,ut,5,2,"div",42),a()(),P(21,mt,4,0,"button",45),a()}if(r&2){let t=d();s(6),y("active",t.selectedTransportMode==="walking"),s(4),h("ngIf",t.routeInfo&&t.selectedTransportMode==="walking"),s(),y("active",t.selectedTransportMode==="cycling"),s(4),h("ngIf",t.routeInfo&&t.selectedTransportMode==="cycling"),s(),y("active",t.selectedTransportMode==="driving"),s(4),h("ngIf",t.routeInfo&&t.selectedTransportMode==="driving"),s(),h("ngIf",t.selectedTransportMode&&t.routeInfo&&t.selectedCenter)}}function _t(r,M){if(r&1&&(i(0,"span",47),g(1),a()),r&2){let t=d(2);s(),I(" ",t.formatTime(t.routeInfo[t.selectedTransportMode]==null?null:t.routeInfo[t.selectedTransportMode].duration)," ")}}function ft(r,M){if(r&1&&(i(0,"span",35),g(1),a()),r&2){let t=d(2);s(),I(" (",t.formatDistance(t.routeInfo[t.selectedTransportMode]==null?null:t.routeInfo[t.selectedTransportMode].distance),") ")}}function Ct(r,M){if(r&1){let t=T();i(0,"div",50)(1,"div",51)(2,"div",52),f(3,"ion-icon",53),a(),i(4,"div",54)(5,"div",55),g(6),a(),i(7,"div",56),P(8,_t,2,1,"span",57)(9,ft,2,1,"span",31),a()()(),i(10,"div",58)(11,"ion-button",59),C("click",function(){w(t);let e=d();return b(e.startRealTimeNavigation(e.selectedCenter))}),f(12,"ion-icon",60),g(13," Start "),a()()()}if(r&2){let t=d();s(3),h("name",t.selectedTransportMode==="walking"?"walk-outline":t.selectedTransportMode==="cycling"?"bicycle-outline":"car-outline"),s(3),v(t.selectedCenter.name),s(2),h("ngIf",t.routeInfo[t.selectedTransportMode]),s(),h("ngIf",t.routeInfo[t.selectedTransportMode])}}var zt=(()=>{class r{constructor(){this.userMarker=null,this.routeLayer=null,this.nearestMarkers=[],this.evacuationCenters=[],this.userLocation=null,this.newCenterId=null,this.highlightCenter=!1,this.centerLat=null,this.centerLng=null,this.selectedCenter=null,this.selectedTransportMode=null,this.routeInfo={},this.showAllCentersPanel=!1,this.showRouteFooter=!1,this.travelMode="walking",this.routeTime=0,this.routeDistance=0,this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.loadingCtrl=x(H),this.toastCtrl=x(Q),this.alertCtrl=x(q),this.http=x(D),this.router=x(B),this.route=x(V),this.osmRouting=x(et),this.mapboxRouting=x(nt),this.enhancedDownload=x(ot)}ngOnInit(){console.log("\u{1F525} FIRE MAP: Component initialized..."),this.route.queryParams.subscribe(t=>{t.newCenterId&&(this.newCenterId=t.newCenterId,this.highlightCenter=t.highlightCenter==="true",this.centerLat=t.centerLat?parseFloat(t.centerLat):null,this.centerLng=t.centerLng?parseFloat(t.centerLng):null,console.log("\u{1F525} FIRE MAP: New center to highlight:",this.newCenterId))})}ngAfterViewInit(){return _(this,null,function*(){console.log("\u{1F525} FIRE MAP: View initialized, loading map..."),setTimeout(()=>_(this,null,function*(){yield this.loadFireMap()}),100)})}loadFireMap(){return _(this,null,function*(){let t=yield this.loadingCtrl.create({message:"Loading fire evacuation centers...",spinner:"crescent"});yield t.present();try{let n=yield tt.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),e=n.coords.latitude,o=n.coords.longitude;this.userLocation={lat:e,lng:o},console.log(`\u{1F525} FIRE MAP: User location [${e}, ${o}]`),this.initializeMap(e,o),yield this.loadFireCenters(e,o),yield t.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F525} Showing ${this.evacuationCenters.length} fire evacuation centers`,duration:3e3,color:"danger",position:"top"})).present()}catch(n){yield t.dismiss(),console.error("\u{1F525} FIRE MAP: Error loading map",n),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadFireMap()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]})).present()}})}initializeMap(t,n){if(console.log(`\u{1F525} FIRE MAP: Initializing map at [${t}, ${n}]`),!document.getElementById("fire-map"))throw console.error("\u{1F525} FIRE MAP: Container #fire-map not found!"),new Error("Map container not found. Please ensure the view is properly loaded.");this.map&&this.map.remove(),this.map=c.map("fire-map").setView([t,n],13),c.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors"}).addTo(this.map),this.userMarker=c.marker([t,n],{icon:c.icon({iconUrl:"assets/Location.png",iconSize:[30,30],iconAnchor:[15,30]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup()}loadFireCenters(t,n){return _(this,null,function*(){try{console.log("\u{1F525} FIRE MAP: Fetching fire centers...");let e=[];try{e=(yield E(this.http.get(`${Z.apiUrl}/evacuation-centers`))).data||[],console.log("\u{1F525} FIRE MAP: Total centers received from API:",e?.length||0)}catch(o){console.error("\u274C API failed:",o),yield(yield this.alertCtrl.create({header:"Connection Error",message:"Cannot connect to server. Please check your internet connection.",buttons:["OK"]})).present();return}if(this.evacuationCenters=e.filter(o=>Array.isArray(o.disaster_type)?o.disaster_type.some(l=>l==="Fire"):o.disaster_type==="Fire"),console.log(`\u{1F525} FIRE MAP: Filtered to ${this.evacuationCenters.length} fire centers`),console.log("\u{1F525} FIRE MAP: Filtered centers:",this.evacuationCenters.map(o=>`${o.name} (${JSON.stringify(o.disaster_type)})`)),console.log(`\u{1F525} FIRE MAP: Filtered to ${this.evacuationCenters.length} fire centers`),this.evacuationCenters.length===0){yield(yield this.alertCtrl.create({header:"No Fire Centers",message:"No fire evacuation centers found in the data.",buttons:["OK"]})).present();return}yield this.addMarkersAndRoutes(t,n)}catch(e){console.error("\u{1F525} FIRE MAP: Error loading centers",e),yield(yield this.toastCtrl.create({message:"Error loading fire centers. Please check your internet connection.",duration:4e3,color:"danger"})).present()}})}addMarkersAndRoutes(t,n){return _(this,null,function*(){if(this.evacuationCenters.forEach(e=>{let o=Number(e.latitude),l=Number(e.longitude);if(!isNaN(o)&&!isNaN(l)){let p=c.marker([o,l],{icon:c.icon({iconUrl:"assets/forFire.png",iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),u=this.calculateDistance(t,n,o,l);p.on("click",()=>{console.log("\u{1F525} FIRE: Marker clicked for center:",e.name),this.showNavigationPanel(e)});let m=this.newCenterId&&e.id.toString()===this.newCenterId;p.bindPopup(`
          <div class="evacuation-popup">
            <h3>\u{1F525} ${e.name} ${m?"\u2B50 NEW!":""}</h3>
            <p><strong>Type:</strong> Fire Center</p>
            <p><strong>Distance:</strong> ${(u/1e3).toFixed(2)} km</p>
            <p><strong>Capacity:</strong> ${e.capacity||"N/A"}</p>
            <p><em>Click marker for route options</em></p>
            ${m?"<p><strong>\u{1F195} Recently Added!</strong></p>":""}
          </div>
        `),m&&(p.openPopup(),this.map.setView([o,l],15),this.toastCtrl.create({message:`\u{1F195} New fire evacuation center: ${e.name}`,duration:5e3,color:"danger",position:"top"}).then(O=>O.present())),p.addTo(this.map),console.log(`\u{1F525} Added fire marker: ${e.name}`)}}),console.log("\u{1F525} Showing simple markers without auto-routing..."),this.evacuationCenters.length>0){let e=c.latLngBounds([]);e.extend([t,n]),this.evacuationCenters.forEach(o=>{e.extend([Number(o.latitude),Number(o.longitude)])}),this.map.fitBounds(e,{padding:[50,50]})}})}calculateDistance(t,n,e,o){let p=t*Math.PI/180,u=e*Math.PI/180,m=(e-t)*Math.PI/180,O=(o-n)*Math.PI/180,k=Math.sin(m/2)*Math.sin(m/2)+Math.cos(p)*Math.cos(u)*Math.sin(O/2)*Math.sin(O/2);return 6371e3*(2*Math.atan2(Math.sqrt(k),Math.sqrt(1-k)))}routeToTwoNearestCenters(){return _(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){console.log("\u{1F525} FIRE MAP: No user location or evacuation centers available");return}try{console.log("\u{1F525} FIRE MAP: Finding 2 nearest fire centers...");let t=this.getTwoNearestCenters(this.userLocation.lat,this.userLocation.lng);if(t.length===0){yield(yield this.toastCtrl.create({message:"No fire evacuation centers found nearby",duration:3e3,color:"warning"})).present();return}this.clearRoutes(),this.addPulsingMarkers(t),yield this.calculateRoutes(t),yield(yield this.toastCtrl.create({message:`\u{1F525} Showing routes to ${t.length} nearest fire centers`,duration:4e3,color:"danger"})).present()}catch(t){console.error("\u{1F525} FIRE MAP: Error calculating routes",t),yield(yield this.toastCtrl.create({message:"Failed to calculate routes. Please try again.",duration:3e3,color:"danger"})).present()}})}getTwoNearestCenters(t,n){return this.evacuationCenters.map(o=>R(N({},o),{distance:this.calculateDistance(t,n,Number(o.latitude),Number(o.longitude))})).sort((o,l)=>o.distance-l.distance).slice(0,2)}addPulsingMarkers(t){this.nearestMarkers.forEach(n=>this.map.removeLayer(n)),this.nearestMarkers=[],t.forEach((n,e)=>{let o=Number(n.latitude),l=Number(n.longitude);if(!isNaN(o)&&!isNaN(l)){let p=c.divIcon({className:"pulsing-marker",html:`
            <div class="pulse-container">
              <div class="pulse" style="background-color: #dc3545"></div>
              <img src="assets/forFire.png" class="marker-icon" />
              <div class="marker-label">${e+1}</div>
            </div>
          `,iconSize:[50,50],iconAnchor:[25,50]}),u=c.marker([o,l],{icon:p});u.bindPopup(`
          <div class="evacuation-popup nearest-popup">
            <h3>\u{1F3AF} Nearest Center #${e+1}</h3>
            <h4>${n.name}</h4>
            <p><strong>Type:</strong> Fire</p>
            <p><strong>Distance:</strong> ${(n.distance/1e3).toFixed(2)} km</p>
            <p><strong>Capacity:</strong> ${n.capacity||"N/A"}</p>
          </div>
        `),u.addTo(this.map),this.nearestMarkers.push(u)}})}calculateRoutes(t){return _(this,null,function*(){if(this.userLocation){this.routeLayer=c.layerGroup().addTo(this.map);for(let n=0;n<t.length;n++){let e=t[n],o=Number(e.latitude),l=Number(e.longitude);if(!isNaN(o)&&!isNaN(l))try{console.log(`\u{1F525} FIRE MAP: Creating Mapbox route to center ${n+1}: ${e.name}`);let p=this.mapboxRouting.convertTravelModeToProfile(this.travelMode),u=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,l,o,p);if(u&&u.routes&&u.routes.length>0){let m=u.routes[0];c.polyline(m.geometry.coordinates.map(k=>[k[1],k[0]]),{color:"#dc3545",weight:4,opacity:.8,dashArray:n===0?void 0:"10, 10"}).addTo(this.routeLayer),n===0&&(this.routeTime=m.duration,this.routeDistance=m.distance),console.log(`\u2705 FIRE MAP: Added Mapbox route to ${e.name} (${(m.distance/1e3).toFixed(2)}km, ${(m.duration/60).toFixed(0)}min)`)}}catch(p){console.error(`\u{1F525} Error calculating Mapbox route to center ${n+1}:`,p)}}}})}clearRoutes(){this.routeLayer&&(this.map.removeLayer(this.routeLayer),this.routeLayer=null),this.nearestMarkers.forEach(t=>{this.map.removeLayer(t)}),this.nearestMarkers=[],this.map.eachLayer(t=>{(t instanceof c.GeoJSON||t instanceof c.Polyline||t.options&&(t.options.color==="#ef4444"||t.options.color==="#dc3545"||t.isRouteLayer||t.isNavigationRoute))&&this.map.removeLayer(t)})}showNavigationPanel(t){return _(this,null,function*(){console.log("\u{1F525} FIRE: showNavigationPanel called for:",t.name),console.log("\u{1F525} FIRE: Setting selectedCenter to:",t),this.selectedCenter=t,this.selectedTransportMode="walking",this.routeInfo={},this.showRouteFooter=!0,console.log("\u{1F525} FIRE: selectedCenter is now:",this.selectedCenter),console.log("\u{1F525} FIRE: showRouteFooter is now:",this.showRouteFooter),yield this.calculateAllRoutes(t)})}calculateAllRoutes(t){return _(this,null,function*(){if(!this.userLocation)return;let n=["walking","cycling","driving"];for(let e of n)try{let o=this.mapboxRouting.convertTravelModeToProfile(e),l=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),o);if(l&&l.routes&&l.routes.length>0){let p=l.routes[0];this.routeInfo[e]={duration:p.duration,distance:p.distance}}}catch(o){console.error(`\u{1F525} Error calculating ${e} route:`,o)}})}navigateWithMode(t){return _(this,null,function*(){if(!(!this.selectedCenter||!this.userLocation)){this.selectedTransportMode=t,this.clearRoutes();try{let n=this.mapboxRouting.convertTravelModeToProfile(t),e=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(this.selectedCenter.longitude),Number(this.selectedCenter.latitude),n);if(e&&e.routes&&e.routes.length>0){let o=e.routes[0],l="#dc3545";this.routeLayer=c.layerGroup().addTo(this.map);let p=c.polyline(o.geometry.coordinates.map(m=>[m[1],m[0]]),{color:l,weight:5,opacity:.8});p.addTo(this.routeLayer),yield(yield this.toastCtrl.create({message:`\u{1F525} Route: ${(o.distance/1e3).toFixed(2)}km, ${(o.duration/60).toFixed(0)}min via ${t}`,duration:4e3,color:"danger"})).present(),this.map.fitBounds(p.getBounds(),{padding:[50,50]})}}catch(n){console.error("\u{1F525} Error showing route:",n),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}}})}closeNavigationPanel(){this.selectedCenter=null,this.selectedTransportMode=null,this.routeInfo={},this.clearRoutes()}goBack(){this.router.navigate(["/tabs/home"])}onTravelModeChange(t){let n=t.detail.value;this.changeTravelMode(n)}changeTravelMode(t){return _(this,null,function*(){this.travelMode=t,yield(yield this.toastCtrl.create({message:`\u{1F525} Travel mode changed to ${t}`,duration:2e3,color:"danger"})).present(),this.userLocation&&this.evacuationCenters.length>0&&(yield this.routeToTwoNearestCenters())})}downloadMap(){return _(this,null,function*(){if(!this.map){yield(yield this.toastCtrl.create({message:"Map not loaded yet. Please wait and try again.",duration:3e3,color:"warning"})).present();return}try{yield this.enhancedDownload.downloadMapWithRoutes("fire-map",this.map,"Fire",!0)}catch(t){console.error("Enhanced download error:",t),yield(yield this.toastCtrl.create({message:"Failed to download map. Please try again.",duration:3e3,color:"danger"})).present()}})}showAllCenters(){this.showAllCentersPanel=!0}closeAllCentersPanel(){this.showAllCentersPanel=!1}routeToNearestCenters(){return _(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){yield(yield this.toastCtrl.create({message:"Unable to calculate routes. Please ensure location is available.",duration:3e3,color:"warning"})).present();return}try{yield(yield this.toastCtrl.create({message:"\u{1F525} Calculating routes to nearest fire centers...",duration:2e3,color:"danger"})).present(),yield this.routeToTwoNearestCenters()}catch(t){console.error("\u{1F525} Error routing to nearest centers:",t),yield(yield this.toastCtrl.create({message:"Failed to calculate routes. Please try again.",duration:3e3,color:"danger"})).present()}})}calculateDistanceInKm(t){if(!this.userLocation)return"N/A";let n=Number(t.latitude),e=Number(t.longitude);return isNaN(n)||isNaN(e)?"N/A":(this.calculateDistance(this.userLocation.lat,this.userLocation.lng,n,e)/1e3).toFixed(1)}selectCenterFromList(t){this.closeAllCentersPanel(),this.showNavigationPanel(t);let n=Number(t.latitude),e=Number(t.longitude);this.map.setView([n,e],15)}formatTime(t){if(!t)return"";let n=Math.round(t/60);if(n<60)return`${n}m`;{let e=Math.floor(n/60),o=n%60;return`${e}h ${o}m`}}formatDistance(t){return t?t<1e3?`${Math.round(t)}m`:`${(t/1e3).toFixed(1)}km`:""}selectTransportMode(t){this.selectedTransportMode=t,this.selectedCenter&&this.navigateWithMode(t)}ionViewWillLeave(){this.clearRoutes(),this.isRealTimeNavigationActive&&this.osmRouting.stopRealTimeRouting(),this.map&&this.map.remove()}routeToCenter(t,n){return _(this,null,function*(){if(this.userLocation)try{this.clearRoutes();let e=Number(t.latitude),o=Number(t.longitude);if(!isNaN(e)&&!isNaN(o)){console.log(`\u{1F525} FIRE: Creating Mapbox route to ${t.name} via ${n}`);let l=this.mapboxRouting.convertTravelModeToProfile(n),p=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,o,e,l);if(p&&p.routes&&p.routes.length>0){let u=p.routes[0],m="#ef4444";this.routeLayer=c.layerGroup().addTo(this.map);let O=c.polyline(u.geometry.coordinates.map(F=>[F[1],F[0]]),{color:m,weight:5,opacity:.8});O.isRouteLayer=!0,O.addTo(this.routeLayer),yield(yield this.toastCtrl.create({message:`\u{1F525} Route: ${(u.distance/1e3).toFixed(2)}km, ${(u.duration/60).toFixed(0)}min via ${n}`,duration:4e3,color:"danger"})).present(),this.map.fitBounds(O.getBounds(),{padding:[50,50]}),console.log(`\u2705 FIRE: Successfully created route with ${u.geometry.coordinates.length} points`)}}}catch(e){console.error("\u{1F525} Error routing to fire center:",e),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}startRealTimeNavigation(t){return _(this,null,function*(){if(console.log("\u{1F9ED} Starting real-time navigation to fire center:",t.name),!this.selectedTransportMode){console.error("\u274C No transport mode selected");return}yield this.routeToCenter(t,this.selectedTransportMode),this.navigationDestination={lat:Number(t.latitude),lng:Number(t.longitude),name:t.name},this.isRealTimeNavigationActive=!0,this.closeNavigationPanel(),this.toastCtrl.create({message:`\u{1F9ED} Real-time navigation started to ${t.name} via ${this.selectedTransportMode}`,duration:3e3,color:"primary"}).then(n=>n.present()),console.log("\u2705 Fire map real-time navigation setup complete")})}onNavigationRouteUpdated(t){console.log("\u{1F504} Fire map navigation route updated"),this.currentNavigationRoute=t,this.updateMapWithNavigationRoute(t)}onNavigationStopped(){console.log("\u23F9\uFE0F Fire map real-time navigation stopped"),this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.clearNavigationRoute(),this.toastCtrl.create({message:"\u23F9\uFE0F Navigation stopped",duration:2e3,color:"medium"}).then(t=>t.present())}updateMapWithNavigationRoute(t){if(this.clearNavigationRoute(),t.geometry&&t.geometry.coordinates){let n=this.osmRouting.convertToGeoJSON(t),e=c.geoJSON(n,{style:{color:"#dc3545",weight:6,opacity:.8,dashArray:"10, 5"}}).addTo(this.map);e.isNavigationRoute=!0}}clearNavigationRoute(){this.map.eachLayer(t=>{t.isNavigationRoute&&this.map.removeLayer(t)})}static{this.\u0275fac=function(n){return new(n||r)}}static{this.\u0275cmp=L({type:r,selectors:[["app-fire-map"]],standalone:!0,features:[S],decls:36,vars:14,consts:[[3,"translucent"],["color","danger"],["slot","start"],["fill","clear",3,"click"],["name","arrow-back-outline","color","light"],[3,"fullscreen"],["id","fire-map",2,"height","100%","width","100%"],[1,"map-controls"],["fill","clear",1,"control-btn",3,"click"],["src","assets/home-insuranceForFire.png","alt","All Centers",1,"control-icon"],["src","assets/downloadForFire.png","alt","Download",1,"control-icon"],["src","assets/compassForFire.png","alt","Route to Nearest",1,"control-icon"],[3,"destination","travelMode","autoStart","routeUpdated","navigationStopped",4,"ngIf"],[1,"all-centers-panel"],[1,"panel-content"],[1,"panel-header"],[1,"header-info"],["fill","clear","size","small",3,"click"],["name","close-outline"],[1,"centers-list"],["class","center-item",3,"click",4,"ngFor","ngForOf"],[1,"navigation-panel"],["class","center-info",4,"ngIf"],["class","transport-options",4,"ngIf"],[1,"route-footer"],["class","footer-content",4,"ngIf"],[3,"routeUpdated","navigationStopped","destination","travelMode","autoStart"],[1,"center-item",3,"click"],[1,"center-info"],[1,"address"],[1,"center-details"],["class","distance",4,"ngIf"],[1,"capacity"],[1,"center-actions"],["name","chevron-forward-outline"],[1,"distance"],[1,"transport-options"],[1,"option-header"],["name","navigate-outline"],[1,"transport-buttons"],[1,"transport-btn",3,"click"],["name","walk-outline"],["class","route-info",4,"ngIf"],["name","bicycle-outline"],["name","car-outline"],["class","start-navigation-btn",3,"click",4,"ngIf"],[1,"route-info"],[1,"time"],[1,"start-navigation-btn",3,"click"],["name","navigate"],[1,"footer-content"],[1,"route-summary"],[1,"transport-icon"],[3,"name"],[1,"route-details"],[1,"destination"],[1,"route-info-footer"],["class","time",4,"ngIf"],[1,"footer-actions"],["fill","solid","color","danger","size","small",3,"click"],["name","navigate","slot","start"]],template:function(n,e){n&1&&(i(0,"ion-header",0)(1,"ion-toolbar",1)(2,"ion-buttons",2)(3,"ion-button",3),C("click",function(){return e.goBack()}),f(4,"ion-icon",4),a()()()(),i(5,"ion-content",5),f(6,"div",6),i(7,"div",7)(8,"ion-button",8),C("click",function(){return e.showAllCenters()}),f(9,"img",9),a(),i(10,"ion-button",8),C("click",function(){return e.downloadMap()}),f(11,"img",10),a(),i(12,"ion-button",8),C("click",function(){return e.routeToNearestCenters()}),f(13,"img",11),a()(),P(14,st,1,3,"app-real-time-navigation",12),i(15,"div",13)(16,"div",14)(17,"div",15)(18,"div",16)(19,"h3"),g(20,"\u{1F525} All Fire Centers"),a(),i(21,"p"),g(22),a()(),i(23,"ion-button",17),C("click",function(){return e.closeAllCentersPanel()}),f(24,"ion-icon",18),a()(),i(25,"div",19),P(26,lt,12,4,"div",20),a()()(),i(27,"div",21)(28,"div",14)(29,"div",15),P(30,gt,5,2,"div",22),i(31,"ion-button",17),C("click",function(){return e.closeNavigationPanel()}),f(32,"ion-icon",18),a()(),P(33,ht,22,10,"div",23),a()(),i(34,"div",24),P(35,Ct,14,4,"div",25),a()()),n&2&&(h("translucent",!0),s(5),h("fullscreen",!0),s(9),h("ngIf",e.navigationDestination),s(),y("show",e.showAllCentersPanel),s(7),I("",e.evacuationCenters.length," centers available"),s(4),h("ngForOf",e.evacuationCenters),s(),y("show",e.selectedCenter),s(3),h("ngIf",e.selectedCenter),s(3),h("ngIf",e.selectedCenter),s(),y("show",e.selectedCenter&&e.selectedTransportMode),s(),h("ngIf",e.selectedCenter&&e.selectedTransportMode))},dependencies:[X,W,j,G,J,K,Y,$,z,A,U,it],styles:["#fire-map[_ngcontent-%COMP%]{height:100%;width:100%;z-index:1}.map-controls[_ngcontent-%COMP%]{position:absolute;top:80px;right:10px;z-index:1000;display:flex;flex-direction:column;gap:8px}.control-btn[_ngcontent-%COMP%]{--background: rgba(255, 255, 255, .95);--color: #dc3545;--border-radius: 12px;width:50px;height:50px;box-shadow:0 4px 12px #00000026;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(220,53,69,.2)}.control-btn[_ngcontent-%COMP%]:hover{--background: rgba(220, 53, 69, .1);transform:translateY(-2px);box-shadow:0 6px 16px #0003}.control-icon[_ngcontent-%COMP%]{width:28px;height:28px;object-fit:contain}.all-centers-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-400px;width:380px;height:100vh;background:#fff;box-shadow:-4px 0 20px #00000026;z-index:1500;transition:right .3s cubic-bezier(.25,.46,.45,.94);display:flex;flex-direction:column}.all-centers-panel.show[_ngcontent-%COMP%]{right:0}.all-centers-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;padding:20px;overflow:hidden}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:20px;padding-bottom:16px;border-bottom:2px solid var(--ion-color-danger-tint)}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]{flex:1}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:20px;font-weight:700;color:var(--ion-color-danger);line-height:1.2}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);font-weight:500}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);margin:0}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding-right:4px}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:var(--ion-color-light);border-radius:2px}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:var(--ion-color-danger-tint);border-radius:2px}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;margin-bottom:12px;background:#fff;border-radius:12px;border:1px solid var(--ion-color-light);box-shadow:0 2px 8px #00000014;cursor:pointer;transition:all .2s ease}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 16px #dc354526;border-color:var(--ion-color-danger-tint)}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]{flex:1}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 4px;font-size:16px;font-weight:600;color:var(--ion-color-dark);line-height:1.3}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .address[_ngcontent-%COMP%]{margin:0 0 8px;font-size:13px;color:var(--ion-color-medium);line-height:1.4}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%], .all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .capacity[_ngcontent-%COMP%]{font-size:12px;font-weight:500;padding:2px 8px;border-radius:8px;background:var(--ion-color-danger-tint);color:var(--ion-color-danger);width:fit-content}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-actions[_ngcontent-%COMP%]{margin-left:12px}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-actions[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px;color:var(--ion-color-medium)}.all-centers-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:#0006;z-index:1400;opacity:0;visibility:hidden;transition:all .3s ease}.all-centers-overlay.show[_ngcontent-%COMP%]{opacity:1;visibility:visible}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: #dc3545;--color: white;--border-width: 0}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;font-size:18px}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.navigation-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-400px;width:380px;height:100vh;background:#fff;box-shadow:-4px 0 20px #00000026;z-index:1600;transition:right .3s cubic-bezier(.25,.46,.45,.94);display:flex;flex-direction:column}.navigation-panel.show[_ngcontent-%COMP%]{right:0}.navigation-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;padding:20px;overflow:hidden}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:20px;padding-bottom:16px;border-bottom:2px solid var(--ion-color-danger-tint)}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;font-size:20px;font-weight:700;color:var(--ion-color-danger);line-height:1.2}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);font-weight:500;line-height:1.4}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);margin:0}.navigation-panel[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]{margin-bottom:24px}.navigation-panel[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:8px 0;font-size:14px;color:var(--ion-color-dark)}.navigation-panel[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px;color:var(--ion-color-danger);min-width:18px}.navigation-panel[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{line-height:1.4}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]{flex:1;overflow-y:auto}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 16px;font-size:16px;font-weight:600;color:var(--ion-color-dark)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;margin-bottom:12px;background:#fff;border:2px solid var(--ion-color-light);border-radius:12px;cursor:pointer;transition:all .2s ease}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option[_ngcontent-%COMP%]:hover{border-color:var(--ion-color-danger-tint);background:#dc35450d;transform:translateY(-1px);box-shadow:0 4px 12px #0000001a}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option.selected[_ngcontent-%COMP%]{border-color:var(--ion-color-danger);background:#dc35451a;box-shadow:0 4px 16px #dc354533}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]{width:44px;height:44px;border-radius:50%;background:var(--ion-color-danger-tint);display:flex;align-items:center;justify-content:center;margin-right:16px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:var(--ion-color-danger)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option[_ngcontent-%COMP%]   .transport-info[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option[_ngcontent-%COMP%]   .transport-info[_ngcontent-%COMP%]   .mode[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:var(--ion-color-dark);margin-bottom:4px;line-height:1.2}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option[_ngcontent-%COMP%]   .transport-info[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]{font-size:13px;color:var(--ion-color-medium);font-weight:500}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option[_ngcontent-%COMP%]   .transport-action[_ngcontent-%COMP%]{margin-left:12px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option[_ngcontent-%COMP%]   .transport-action[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px;color:var(--ion-color-medium)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option.selected[_ngcontent-%COMP%]   .transport-action[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:var(--ion-color-danger)}.navigation-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:#0006;z-index:1500;opacity:0;visibility:hidden;transition:all .3s ease}.navigation-overlay.show[_ngcontent-%COMP%]{opacity:1;visibility:visible}.route-footer[_ngcontent-%COMP%]{position:fixed;bottom:-100px;left:0;right:0;background:#fff;border-top:1px solid var(--ion-color-light);box-shadow:0 -4px 20px #0000001a;z-index:1500;transition:bottom .3s ease-in-out}.route-footer.show[_ngcontent-%COMP%]{bottom:0}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]{padding:16px 20px;display:flex;align-items:center;justify-content:space-between}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;flex:1}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:var(--ion-color-danger-tint);display:flex;align-items:center;justify-content:center}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:var(--ion-color-danger)}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]{flex:1}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .destination[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:var(--ion-color-dark);margin-bottom:2px;line-height:1.2}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:var(--ion-color-danger)}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium)}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 20px;height:36px;font-weight:600}.transport-info[_ngcontent-%COMP%]{flex:1}.transport-info[_ngcontent-%COMP%]   .mode[_ngcontent-%COMP%]{display:block;font-weight:600;font-size:1rem;margin-bottom:2px}.transport-info[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]{display:block;font-size:.85rem;color:#666}.leaflet-container[_ngcontent-%COMP%]{height:100%;width:100%}.leaflet-popup-content[_ngcontent-%COMP%]{margin:8px 12px;line-height:1.4}.evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 10px;color:#dc3545;font-size:1.1rem}.evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:5px 0;font-size:.9rem}.evacuation-popup[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#333}@media (max-width: 768px){.map-controls[_ngcontent-%COMP%]{top:70px;right:8px}.all-centers-panel[_ngcontent-%COMP%]{width:100%;right:-100%}.all-centers-panel.show[_ngcontent-%COMP%]{right:0}.navigation-panel[_ngcontent-%COMP%]{width:100%;right:-100%}.navigation-panel.show[_ngcontent-%COMP%]{right:0}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]{padding:12px 16px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]{gap:10px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]{width:36px;height:36px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .destination[_ngcontent-%COMP%]{font-size:14px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:13px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:11px}}.travel-mode-selector[_ngcontent-%COMP%]{position:absolute;top:20px;left:20px;z-index:1000;background:#fffffff2;border-radius:12px;padding:8px}.travel-mode-selector[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]{--background: transparent;min-height:40px}.travel-mode-selector[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);--color-checked: var(--ion-color-danger);--indicator-color: var(--ion-color-danger);min-height:40px}.travel-mode-selector[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;margin-bottom:2px}.travel-mode-selector[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:12px;font-weight:500}.route-info[_ngcontent-%COMP%]{position:absolute;bottom:20px;left:20px;z-index:1000;max-width:200px}.route-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;border-radius:12px;background:#fffffff2}.route-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.route-info[_ngcontent-%COMP%]   .route-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-danger);margin-bottom:8px}.route-info[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.route-info[_ngcontent-%COMP%]   .route-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:14px;color:var(--ion-color-dark)}.route-info[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;color:var(--ion-color-danger)}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse-container[_ngcontent-%COMP%]{position:relative;width:50px;height:50px;display:flex;align-items:center;justify-content:center}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse[_ngcontent-%COMP%]{position:absolute;width:40px;height:40px;border-radius:50%;animation:_ngcontent-%COMP%_pulse 2s infinite;opacity:.6}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-icon[_ngcontent-%COMP%]{width:30px;height:30px;z-index:2;position:relative}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-label[_ngcontent-%COMP%]{position:absolute;top:-8px;right:-8px;background:#dc3545;color:#fff;border-radius:50%;width:20px;height:20px;display:flex;align-items:center;justify-content:center;font-size:12px;font-weight:700;z-index:3;border:2px solid white}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(.8);opacity:.8}50%{transform:scale(1.2);opacity:.4}to{transform:scale(.8);opacity:.8}}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]{text-align:center;min-width:200px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-danger);font-size:16px;font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;font-size:14px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-dark)}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup.nearest-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#dc3545;font-size:18px}"]})}}return r})();export{zt as FireMapPage};
