import{a as T}from"./chunk-WPPT3EJF.js";import{i as M,k as P,ra as A}from"./chunk-E442IOFQ.js";import{a as I,g as m}from"./chunk-2R6CW7ES.js";var k=(()=>{class f{constructor(t){this.http=t,this.ORS_API_KEY=T.orsApiKey,this.ORS_BASE_URL="https://api.openrouteservice.org/v2/directions",this.currentRoute=null,this.isRealTimeActive=!1,this.realTimeInterval=null,this.lastUserPosition=null,this.routeUpdateCallback=null}getDirections(p,u,h,R){return m(this,arguments,function*(t,e,s,r,o="foot-walking",n={}){console.log(`\u{1F5FA}\uFE0F Attempting route calculation from [${e}, ${t}] to [${r}, ${s}] using ${o}`);try{let y=I({coordinates:[[t,e],[s,r]],format:"geojson"},n),v=`${this.ORS_BASE_URL}/${o}/geojson`;console.log(`\u{1F310} Making request to: ${v}`),console.log(`\u{1F511} Using API key: ${this.ORS_API_KEY.substring(0,10)}...`);let d=yield this.http.post(v,y,{headers:{Authorization:this.ORS_API_KEY,"Content-Type":"application/json",Accept:"application/json, application/geo+json, application/gpx+xml, img/png; charset=utf-8"}}).toPromise();if(console.log("\u{1F4E1} OpenRouteService response:",d),d&&d.features&&d.features.length>0){let a=d.features[0],i=0,c=0;a.properties&&(a.properties.summary?(i=a.properties.summary.distance||0,c=a.properties.summary.duration||0):a.properties.segments&&a.properties.segments.length>0&&(i=a.properties.segments[0].distance||0,c=a.properties.segments[0].duration||0)),(!i||!c)&&(console.log("\u26A0\uFE0F Missing distance/duration in response, calculating fallback"),i=this.calculateDistance(e,t,r,s),c=this.estimateDuration(i,o));let g={routes:[{geometry:a.geometry,distance:i,duration:c,legs:[{distance:i,duration:c}]}],code:"Ok"};return console.log("\u2705 Successfully parsed route from OpenRouteService"),console.log(`\u{1F4CF} Distance: ${(i/1e3).toFixed(2)} km, Duration: ${Math.round(c/60)} min`),g}throw new Error("No routes found in response")}catch(l){console.error("\u274C OpenRouteService error:",l),l.status&&console.error(`HTTP Status: ${l.status}`),l.error&&console.error("Error details:",l.error);try{console.log("\u{1F504} Retrying with simplified parameters...");let v={coordinates:[[t,e],[s,r]]},d=`${this.ORS_BASE_URL}/${o}`,a=yield this.http.post(d,v,{headers:{Authorization:this.ORS_API_KEY,"Content-Type":"application/json"}}).toPromise();if(console.log("\u{1F4E1} Retry response:",a),a&&a.features&&a.features.length>0){let i=a.features[0],c=0,g=0;i.properties&&(i.properties.summary?(c=i.properties.summary.distance||0,g=i.properties.summary.duration||0):i.properties.segments&&i.properties.segments.length>0&&(c=i.properties.segments[0].distance||0,g=i.properties.segments[0].duration||0)),(!c||!g)&&(c=this.calculateDistance(e,t,r,s),g=this.estimateDuration(c,o));let w={routes:[{geometry:i.geometry,distance:c,duration:g,legs:[{distance:c,duration:g}]}],code:"Ok"};return console.log("\u2705 Retry successful!"),w}}catch(y){console.error("\u274C Retry also failed:",y)}throw console.log("\u{1F504} All attempts failed, throwing error instead of fallback"),l}})}convertTravelModeToProfile(t){switch(t.toLowerCase()){case"walking":case"foot":return"foot-walking";case"cycling":case"bicycle":case"bike":return"cycling-regular";case"driving":case"car":return"driving-car";default:return"foot-walking"}}convertToGeoJSON(t){return{type:"Feature",geometry:t.geometry,properties:{distance:t.distance,duration:t.duration}}}getRouteSummary(t){let e=(t.distance/1e3).toFixed(2),s=Math.round(t.duration/60);return{distance:`${e} km`,duration:`${s} min`,distanceText:`${e} km`,durationText:`${s} min`}}estimateDuration(t,e){let s={"foot-walking":1.4,"cycling-regular":4.2,"driving-car":13.9},r=s[e]||s["foot-walking"];return t/r}calculateDistance(t,e,s,r){let n=t*Math.PI/180,p=s*Math.PI/180,u=(s-t)*Math.PI/180,h=(r-e)*Math.PI/180,R=Math.sin(u/2)*Math.sin(u/2)+Math.cos(n)*Math.cos(p)*Math.sin(h/2)*Math.sin(h/2);return 6371e3*(2*Math.atan2(Math.sqrt(R),Math.sqrt(1-R)))}startRealTimeRouting(t,e="foot-walking",s,r=3e4){console.log("\u{1F504} Starting real-time routing to:",t),this.isRealTimeActive=!0,this.routeUpdateCallback=s,this.realTimeInterval&&clearInterval(this.realTimeInterval),this.realTimeInterval=setInterval(()=>m(this,null,function*(){if(this.lastUserPosition&&this.isRealTimeActive)try{let o=yield this.getDirections(this.lastUserPosition.lng,this.lastUserPosition.lat,t.lng,t.lat,e,{geometries:"geojson",overview:"full",steps:!0,continue_straight:!1,alternative_routes:{target_count:2,weight_factor:1.4}});o.routes&&o.routes.length>0&&(this.currentRoute=o.routes[0],this.routeUpdateCallback?.(this.currentRoute),console.log("\u{1F504} Real-time route updated"))}catch(o){console.error("\u274C Real-time route update failed:",o)}}),r)}updateUserPosition(t,e){this.lastUserPosition={lat:t,lng:e},this.isRealTimeActive&&this.shouldUpdateRoute(t,e)&&(console.log("\u{1F4CD} Significant position change detected, updating route..."),this.triggerImmediateRouteUpdate())}stopRealTimeRouting(){console.log("\u23F9\uFE0F Stopping real-time routing"),this.isRealTimeActive=!1,this.currentRoute=null,this.routeUpdateCallback=null,this.lastUserPosition=null,this.realTimeInterval&&(clearInterval(this.realTimeInterval),this.realTimeInterval=null)}shouldUpdateRoute(t,e){return this.lastUserPosition?this.calculateDistance(this.lastUserPosition.lat,this.lastUserPosition.lng,t,e)>50:!0}triggerImmediateRouteUpdate(){return m(this,null,function*(){console.log("\u26A1 Immediate route update triggered")})}getCurrentRoute(){return this.currentRoute}isRealTimeRoutingActive(){return this.isRealTimeActive}getRouteWithTraffic(t,e,s,r,o="driving-car"){return m(this,null,function*(){let n=o==="driving-car"?{geometries:"geojson",overview:"full",steps:!0,continue_straight:!1,avoid_features:["tollways"],alternative_routes:{target_count:2,weight_factor:1.4}}:{geometries:"geojson",overview:"full",steps:!0};return this.getDirections(t,e,s,r,o,n)})}testConnection(){return m(this,null,function*(){try{console.log("\u{1F9EA} Testing OpenRouteService API connection...");let e={coordinates:[[123.8854,10.3157],[123.8954,10.3257]],format:"geojson"},s=`${this.ORS_BASE_URL}/foot-walking/geojson`,r=yield this.http.post(s,e,{headers:{Authorization:this.ORS_API_KEY,"Content-Type":"application/json",Accept:"application/json, application/geo+json, application/gpx+xml, img/png; charset=utf-8"}}).toPromise();return r&&r.features&&r.features.length>0?{success:!0,message:"OpenRouteService API connection successful!",details:{features:r.features.length,apiKey:this.ORS_API_KEY.substring(0,10)+"..."}}:{success:!1,message:"OpenRouteService API returned empty response",details:r}}catch(t){return console.error("\u274C OpenRouteService connection test failed:",t),{success:!1,message:`OpenRouteService API connection failed: ${t.message||t}`,details:{status:t.status,error:t.error,apiKey:this.ORS_API_KEY.substring(0,10)+"..."}}}})}getNavigationInstructions(t){let e=[];return t.legs&&t.legs.length>0&&t.legs.forEach((s,r)=>{s.steps&&s.steps.forEach((o,n)=>{e.push({id:`${r}-${n}`,instruction:o.name||"Continue",distance:o.distance,duration:o.duration,coordinates:o.geometry.coordinates,maneuver:this.getManeuverType(o.name||""),bearing:this.calculateBearing(o.geometry.coordinates)})})}),e}getCurrentInstruction(t,e,s){if(!s.length)return null;let r=s[0],o=1/0;for(let n of s)if(n.coordinates&&n.coordinates.length>0){let[p,u]=n.coordinates[0],h=this.calculateDistance(t,e,u,p);h<o&&(o=h,r=n)}return o<100?r:null}calculateBearing(t){if(t.length<2)return 0;let[e,s]=t[0],[r,o]=t[1],n=(r-e)*Math.PI/180,p=s*Math.PI/180,u=o*Math.PI/180,h=Math.sin(n)*Math.cos(u),R=Math.cos(p)*Math.sin(u)-Math.sin(p)*Math.cos(u)*Math.cos(n);return(Math.atan2(h,R)*180/Math.PI+360)%360}getManeuverType(t){let e=t.toLowerCase();return e.includes("left")?"turn-left":e.includes("right")?"turn-right":e.includes("straight")||e.includes("continue")?"straight":e.includes("u-turn")?"u-turn":e.includes("roundabout")?"roundabout":e.includes("exit")?"exit":e.includes("merge")?"merge":"straight"}getETA(t){let e=new Date,s=t.duration*1e3;return new Date(e.getTime()+s)}formatDuration(t){let e=Math.floor(t/3600),s=Math.floor(t%3600/60);return e>0?`${e}h ${s}m`:`${s}m`}formatDistance(t){return t>=1e3?`${(t/1e3).toFixed(1)} km`:`${Math.round(t)} m`}static{this.\u0275fac=function(e){return new(e||f)(P(A))}}static{this.\u0275prov=M({token:f,factory:f.\u0275fac,providedIn:"root"})}}return f})();export{k as a};
