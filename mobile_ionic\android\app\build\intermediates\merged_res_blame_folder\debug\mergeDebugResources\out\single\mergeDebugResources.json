[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\mipmap-xxxhdpi_alerto_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\mipmap-xxxhdpi\\alerto_launcher_round.png"}, {"merged": "io.ionic.starter.app-debug-39:/drawable-land-xxhdpi_splash.png.flat", "source": "io.ionic.starter.app-main-41:/drawable-land-xxhdpi/splash.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\mipmap-hdpi_alerto_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\mipmap-hdpi\\alerto_launcher_round.png"}, {"merged": "io.ionic.starter.app-debug-39:/drawable-port-xxxhdpi_splash.png.flat", "source": "io.ionic.starter.app-main-41:/drawable-port-xxxhdpi/splash.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\drawable-land-hdpi_splash.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\drawable-land-hdpi\\splash.png"}, {"merged": "io.ionic.starter.app-debug-39:/drawable-land-mdpi_splash.png.flat", "source": "io.ionic.starter.app-main-41:/drawable-land-mdpi/splash.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\drawable-land-mdpi_splash.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\drawable-land-mdpi\\splash.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\drawable-port-xxxhdpi_splash.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\drawable-port-xxxhdpi\\splash.png"}, {"merged": "io.ionic.starter.app-debug-39:/drawable-port-xxhdpi_splash.png.flat", "source": "io.ionic.starter.app-main-41:/drawable-port-xxhdpi/splash.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\mipmap-xxhdpi_alerto_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\mipmap-xxhdpi\\alerto_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\mipmap-mdpi_alerto_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\mipmap-mdpi\\alerto_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\drawable-port-xxhdpi_splash.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\drawable-port-xxhdpi\\splash.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\drawable-anydpi-v24_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-pngs-34:\\drawable-anydpi-v24\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\mipmap-xxhdpi_alerto_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\mipmap-xxhdpi\\alerto_launcher_round.png"}, {"merged": "io.ionic.starter.app-debug-39:/drawable_splash.png.flat", "source": "io.ionic.starter.app-main-41:/drawable/splash.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\mipmap-xxxhdpi_alerto_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\mipmap-xxxhdpi\\alerto_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\drawable_splash.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\drawable\\splash.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\drawable-port-xhdpi_splash.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\drawable-port-xhdpi\\splash.png"}, {"merged": "io.ionic.starter.app-debug-39:/drawable-port-xhdpi_splash.png.flat", "source": "io.ionic.starter.app-main-41:/drawable-port-xhdpi/splash.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\mipmap-xhdpi_alerto_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\mipmap-xhdpi\\alerto_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\mipmap-xxhdpi_alerto_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\mipmap-xxhdpi\\alerto_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\mipmap-xhdpi_alerto_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\mipmap-xhdpi\\alerto_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "io.ionic.starter.app-debug-39:/drawable-land-xxxhdpi_splash.png.flat", "source": "io.ionic.starter.app-main-41:/drawable-land-xxxhdpi/splash.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\xml_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\xml\\config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\mipmap-mdpi_alerto_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\mipmap-mdpi\\alerto_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\drawable\\ic_launcher_background.xml"}, {"merged": "io.ionic.starter.app-debug-39:/drawable-port-mdpi_splash.png.flat", "source": "io.ionic.starter.app-main-41:/drawable-port-mdpi/splash.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\drawable-land-xxhdpi_splash.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\drawable-land-xxhdpi\\splash.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\mipmap-mdpi_alerto_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\mipmap-mdpi\\alerto_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\drawable-port-mdpi_splash.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\drawable-port-mdpi\\splash.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\mipmap-hdpi_alerto_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\mipmap-hdpi\\alerto_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\drawable-land-xhdpi_splash.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\drawable-land-xhdpi\\splash.png"}, {"merged": "io.ionic.starter.app-debug-39:/drawable-land-xhdpi_splash.png.flat", "source": "io.ionic.starter.app-main-41:/drawable-land-xhdpi/splash.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\xml_file_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\xml\\file_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\drawable-port-hdpi_splash.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\drawable-port-hdpi\\splash.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\drawable-land-xxxhdpi_splash.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\drawable-land-xxxhdpi\\splash.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\mipmap-hdpi_alerto_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\mipmap-hdpi\\alerto_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\mipmap-xhdpi_alerto_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\mipmap-xhdpi\\alerto_launcher_foreground.png"}, {"merged": "io.ionic.starter.app-debug-39:/drawable-land-hdpi_splash.png.flat", "source": "io.ionic.starter.app-main-41:/drawable-land-hdpi/splash.png"}, {"merged": "io.ionic.starter.app-debug-39:/drawable-port-hdpi_splash.png.flat", "source": "io.ionic.starter.app-main-41:/drawable-port-hdpi/splash.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-debug-39:\\mipmap-xxxhdpi_alerto_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\io.ionic.starter.app-main-41:\\mipmap-xxxhdpi\\alerto_launcher.png"}]