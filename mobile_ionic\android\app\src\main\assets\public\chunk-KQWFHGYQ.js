import{a as ot}from"./chunk-KUBQHLD5.js";import{a as nt,c as st}from"./chunk-7IBF2Q3E.js";import{a as it}from"./chunk-JCIM63K2.js";import{b as tt}from"./chunk-I4SN7ED3.js";import{a as et}from"./chunk-FE3NIZ6B.js";import"./chunk-3J7GGTVR.js";import{a as Z}from"./chunk-WPPT3EJF.js";import"./chunk-2LL5MXLB.js";import{B as s,Db as G,F as P,G as C,Hb as B,I as A,Ib as J,K as i,L as a,M as v,P as T,Q as M,R as _,Y as r,Yb as H,Z as h,_ as S,ac as K,cc as Y,d as D,da as F,ec as Q,fc as X,ib as W,l as O,m as R,ma as z,na as $,pa as V,r as w,ra as q,s as k,vb as j,ya as U}from"./chunk-E442IOFQ.js";import"./chunk-LR6AIEJQ.js";import"./chunk-6NVMNNPA.js";import"./chunk-KW2BML7M.js";import"./chunk-SV2ZKNWA.js";import"./chunk-YJDO75HI.js";import"./chunk-HC6MZPB3.js";import"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-ZJ5IMUT4.js";import"./chunk-SGSBBWFA.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-EI2QJP5N.js";import"./chunk-W6U2AR23.js";import"./chunk-APL3YEA6.js";import"./chunk-HSXX7Y3C.js";import"./chunk-FUGLTCJS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import{a as E,b as I,f as rt,g as f}from"./chunk-2R6CW7ES.js";var g=rt(st());function lt(c,x){if(c&1){let t=T();i(0,"app-real-time-navigation",28),M("routeUpdated",function(o){w(t);let e=_();return k(e.onNavigationRouteUpdated(o))})("navigationStopped",function(){w(t);let o=_();return k(o.onNavigationStopped())}),a()}if(c&2){let t=_();C("destination",t.navigationDestination)("travelMode",t.selectedTransportMode==="walking"?"foot-walking":t.selectedTransportMode==="cycling"?"cycling-regular":"driving-car")("autoStart",t.isRealTimeNavigationActive)}}function ct(c,x){if(c&1&&(i(0,"span",37),r(1),a()),c&2){let t=_().$implicit,n=_();s(),S(" \u{1F4CD} ",n.calculateDistanceInKm(t)," km away ")}}function pt(c,x){if(c&1){let t=T();i(0,"div",29),M("click",function(){let o=w(t).$implicit,e=_();return k(e.selectCenterFromList(o))}),i(1,"div",30)(2,"h4"),r(3),a(),i(4,"p",31),r(5),a(),i(6,"div",32),P(7,ct,2,1,"span",33),i(8,"span",34),r(9),a()()(),i(10,"div",35),v(11,"ion-icon",36),a()()}if(c&2){let t=x.$implicit,n=_();s(3),h(t.name),s(2),h(t.address),s(2),C("ngIf",n.userLocation),s(2),S("\u{1F465} ",t.capacity||"N/A"," capacity")}}function dt(c,x){if(c&1&&(i(0,"div",30)(1,"h3"),r(2),a(),i(3,"p"),r(4),a()()),c&2){let t=_();s(2),h(t.selectedCenter.name),s(2),h(t.selectedCenter.address)}}function gt(c,x){if(c&1&&(i(0,"div",48)(1,"span",49),r(2),a(),i(3,"span",37),r(4),a()()),c&2){let t=_(2);s(2),h(t.formatTime(t.routeInfo.walking==null?null:t.routeInfo.walking.duration)),s(2),h(t.formatDistance(t.routeInfo.walking==null?null:t.routeInfo.walking.distance))}}function ut(c,x){if(c&1&&(i(0,"div",48)(1,"span",49),r(2),a(),i(3,"span",37),r(4),a()()),c&2){let t=_(2);s(2),h(t.formatTime(t.routeInfo.cycling==null?null:t.routeInfo.cycling.duration)),s(2),h(t.formatDistance(t.routeInfo.cycling==null?null:t.routeInfo.cycling.distance))}}function mt(c,x){if(c&1&&(i(0,"div",48)(1,"span",49),r(2),a(),i(3,"span",37),r(4),a()()),c&2){let t=_(2);s(2),h(t.formatTime(t.routeInfo.driving==null?null:t.routeInfo.driving.duration)),s(2),h(t.formatDistance(t.routeInfo.driving==null?null:t.routeInfo.driving.distance))}}function ht(c,x){if(c&1){let t=T();i(0,"button",50),M("click",function(){w(t);let o=_(2);return k(o.startNavigation())}),v(1,"ion-icon",51),i(2,"span"),r(3,"Start Navigation"),a()()}}function _t(c,x){if(c&1){let t=T();i(0,"div",38)(1,"div",39),v(2,"ion-icon",40),i(3,"span"),r(4,"Choose Transportation"),a()(),i(5,"div",41)(6,"button",42),M("click",function(){w(t);let o=_();return k(o.selectTransportMode("walking"))}),v(7,"ion-icon",43),i(8,"span"),r(9,"Walk"),a(),P(10,gt,5,2,"div",44),a(),i(11,"button",42),M("click",function(){w(t);let o=_();return k(o.selectTransportMode("cycling"))}),v(12,"ion-icon",45),i(13,"span"),r(14,"Cycle"),a(),P(15,ut,5,2,"div",44),a(),i(16,"button",42),M("click",function(){w(t);let o=_();return k(o.selectTransportMode("driving"))}),v(17,"ion-icon",46),i(18,"span"),r(19,"Drive"),a(),P(20,mt,5,2,"div",44),a()(),P(21,ht,4,0,"button",47),a()}if(c&2){let t=_();s(6),A("active",t.selectedTransportMode==="walking"),s(4),C("ngIf",t.routeInfo&&t.selectedTransportMode==="walking"),s(),A("active",t.selectedTransportMode==="cycling"),s(4),C("ngIf",t.routeInfo&&t.selectedTransportMode==="cycling"),s(),A("active",t.selectedTransportMode==="driving"),s(4),C("ngIf",t.routeInfo&&t.selectedTransportMode==="driving"),s(),C("ngIf",t.selectedTransportMode&&t.routeInfo)}}var It=(()=>{class c{constructor(){this.userMarker=null,this.routeLayer=null,this.nearestMarkers=[],this.evacuationCenters=[],this.centerCounts={earthquake:0,typhoon:0,flood:0,fire:0,landslide:0,others:0,multiple:0,total:0},this.travelMode="walking",this.routeTime=0,this.routeDistance=0,this.userLocation=null,this.selectedCenter=null,this.selectedTransportMode=null,this.routeInfo={},this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.showAllCentersPanel=!1,this.loadingCtrl=O(Y),this.toastCtrl=O(Q),this.alertCtrl=O(K),this.http=O(q),this.router=O(U),this.osmRouting=O(et),this.mapboxRouting=O(nt),this.enhancedDownload=O(ot)}ngOnInit(){return f(this,null,function*(){console.log("\u{1F5FA}\uFE0F ALL MAPS: Initializing..."),yield this.loadAllMaps()})}loadAllMaps(){return f(this,null,function*(){let t=yield this.loadingCtrl.create({message:"Loading all evacuation centers...",spinner:"crescent"});yield t.present();try{let n=yield tt.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),o=n.coords.latitude,e=n.coords.longitude;this.userLocation={lat:o,lng:e},console.log(`\u{1F5FA}\uFE0F ALL MAPS: User location [${o}, ${e}]`),this.initializeMap(o,e),yield this.loadAllCenters(o,e),yield t.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F5FA}\uFE0F Showing all ${this.centerCounts.total} evacuation centers`,duration:3e3,color:"secondary",position:"top"})).present()}catch(n){yield t.dismiss(),console.error("\u{1F5FA}\uFE0F ALL MAPS: Error loading map",n),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadAllMaps()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]})).present()}})}initializeMap(t,n){console.log(`\u{1F5FA}\uFE0F ALL MAPS: Initializing map at [${t}, ${n}]`),this.map&&this.map.remove(),this.map=g.map("all-maps").setView([t,n],12),g.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors"}).addTo(this.map),this.userMarker=g.marker([t,n],{icon:g.icon({iconUrl:"assets/Location.png",iconSize:[30,30],iconAnchor:[15,30]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup()}loadAllCenters(t,n){return f(this,null,function*(){try{console.log("\u{1F5FA}\uFE0F ALL MAPS: Fetching all evacuation centers...");let o=yield D(this.http.get(`${Z.apiUrl}/evacuation-centers`));if(console.log("\u{1F5FA}\uFE0F ALL MAPS: Total centers received:",o.data?.length||0),this.evacuationCenters=o.data||[],this.centerCounts.earthquake=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.includes("Earthquake"):e.disaster_type==="Earthquake").length,this.centerCounts.typhoon=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.includes("Typhoon"):e.disaster_type==="Typhoon").length,this.centerCounts.flood=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.includes("Flood"):e.disaster_type==="Flood"||e.disaster_type==="Flash Flood").length,this.centerCounts.fire=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.includes("Fire"):e.disaster_type==="Fire").length,this.centerCounts.landslide=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.includes("Landslide"):e.disaster_type==="Landslide").length,this.centerCounts.others=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.some(u=>u==="Others"||typeof u=="string"&&u.startsWith("Others:")):e.disaster_type==="Others"||typeof e.disaster_type=="string"&&e.disaster_type.startsWith("Others:")).length,this.centerCounts.multiple=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.length>1:!1).length,this.centerCounts.total=this.evacuationCenters.length,console.log("\u{1F5FA}\uFE0F ALL MAPS: Center counts:",this.centerCounts),this.evacuationCenters.length===0){yield(yield this.alertCtrl.create({header:"No Evacuation Centers",message:"No evacuation centers found in the database.",buttons:["OK"]})).present();return}if(this.evacuationCenters.forEach(e=>{let u=Number(e.latitude),d=Number(e.longitude);if(!isNaN(u)&&!isNaN(d)){let l="assets/Location.png",p="\u26AA",m=Array.isArray(e.disaster_type)?e.disaster_type:[e.disaster_type];if(m.length>1)l="assets/forMultiple.png",p="\u{1F518}",console.log(`\u{1F5FA}\uFE0F Multi-type center: ${e.name} supports ${m.join(", ")}`);else{let N=Array.isArray(e.disaster_type)?e.disaster_type[0]:e.disaster_type;if(typeof N=="string"&&N.startsWith("Others:"))l="assets/forOthers.png",p="\u{1F7E3}";else switch(N){case"Earthquake":l="assets/forEarthquake.png",p="\u{1F7E0}";break;case"Typhoon":l="assets/forTyphoon.png",p="\u{1F7E2}";break;case"Flood":l="assets/forFlood.png",p="\u{1F535}";break;case"Fire":l="assets/forFire.png",p="\u{1F534}";break;case"Landslide":l="assets/forLandslide.png",p="\u{1F7E4}";break;case"Others":l="assets/forOthers.png",p="\u{1F7E3}";break;default:l="assets/forOthers.png",p="\u{1F7E3}";break}}let b=g.marker([u,d],{icon:g.icon({iconUrl:l,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),L=this.calculateDistance(t,n,u,d);b.on("click",()=>{this.showNavigationPanel(e)});let at=Array.isArray(e.disaster_type)?e.disaster_type.join(", "):e.disaster_type||"General";b.bindPopup(`
            <div class="evacuation-popup">
              <h3>${p} ${e.name}</h3>
              <p><strong>Type:</strong> ${at}</p>
              <p><strong>Distance:</strong> ${(L/1e3).toFixed(2)} km</p>
              <p><strong>Capacity:</strong> ${e.capacity||"N/A"}</p>
              <p><em>Click marker for route options</em></p>
            </div>
          `),b.addTo(this.map),console.log(`\u{1F5FA}\uFE0F Added ${e.disaster_type} marker: ${e.name}`)}}),this.evacuationCenters.length>0){let e=g.latLngBounds([]);e.extend([t,n]),this.evacuationCenters.forEach(u=>{e.extend([Number(u.latitude),Number(u.longitude)])}),this.map.fitBounds(e,{padding:[50,50]})}}catch(o){console.error("\u{1F5FA}\uFE0F ALL MAPS: Error loading centers",o),yield(yield this.toastCtrl.create({message:"Error loading evacuation centers. Please check your connection.",duration:3e3,color:"danger"})).present()}})}calculateDistance(t,n,o,e){let d=t*Math.PI/180,l=o*Math.PI/180,p=(o-t)*Math.PI/180,m=(e-n)*Math.PI/180,y=Math.sin(p/2)*Math.sin(p/2)+Math.cos(d)*Math.cos(l)*Math.sin(m/2)*Math.sin(m/2);return 6371e3*(2*Math.atan2(Math.sqrt(y),Math.sqrt(1-y)))}routeToTwoNearestCenters(){return f(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){console.log("\u{1F5FA}\uFE0F ALL MAPS: No user location or evacuation centers available");return}try{console.log("\u{1F5FA}\uFE0F ALL MAPS: Finding 2 nearest centers...");let t=this.getTwoNearestCenters(this.userLocation.lat,this.userLocation.lng);if(t.length===0){yield(yield this.toastCtrl.create({message:"No evacuation centers found nearby",duration:3e3,color:"warning"})).present();return}this.clearRoutes(),this.addPulsingMarkers(t),yield this.calculateRoutes(t),yield(yield this.toastCtrl.create({message:`\u{1F5FA}\uFE0F Showing routes to ${t.length} nearest centers via ${this.travelMode}`,duration:3e3,color:"success",position:"top"})).present()}catch(t){console.error("\u{1F5FA}\uFE0F ALL MAPS: Error calculating routes",t),yield(yield this.toastCtrl.create({message:"Error calculating routes. Please try again.",duration:3e3,color:"danger"})).present()}})}getTwoNearestCenters(t,n){return this.evacuationCenters.map(e=>I(E({},e),{distance:this.calculateDistance(t,n,Number(e.latitude),Number(e.longitude))})).sort((e,u)=>e.distance-u.distance).slice(0,2)}addPulsingMarkers(t){t.forEach((n,o)=>{let e=Number(n.latitude),u=Number(n.longitude);if(!isNaN(e)&&!isNaN(u)){let d="assets/Location.png",l="#3880ff";typeof n.disaster_type=="string"&&n.disaster_type.startsWith("Others:")?(d="assets/forOthers.png",l="#9333ea"):n.disaster_type==="Earthquake"?(d="assets/forEarthquake.png",l="#ff9500"):n.disaster_type==="Typhoon"?(d="assets/forTyphoon.png",l="#2dd36f"):n.disaster_type==="Flood"?(d="assets/forFlood.png",l="#3dc2ff"):n.disaster_type==="Fire"?(d="assets/forFire.png",l="#ef4444"):n.disaster_type==="Landslide"?(d="assets/forLandslide.png",l="#8b5a2b"):n.disaster_type==="Others"&&(d="assets/forOthers.png",l="#9333ea");let p=g.divIcon({className:"pulsing-marker",html:`
            <div class="pulse-container">
              <div class="pulse" style="background-color: ${l}"></div>
              <img src="${d}" class="marker-icon" />
              <div class="marker-label">${o+1}</div>
            </div>
          `,iconSize:[50,50],iconAnchor:[25,50]}),m=g.marker([e,u],{icon:p});m.bindPopup(`
          <div class="evacuation-popup nearest-popup">
            <h3>\u{1F3AF} Nearest Center #${o+1}</h3>
            <h4>${n.name}</h4>
            <p><strong>Type:</strong> ${n.disaster_type}</p>
            <p><strong>Distance:</strong> ${(n.distance/1e3).toFixed(2)} km</p>
            <p><strong>Capacity:</strong> ${n.capacity||"N/A"}</p>
          </div>
        `),m.addTo(this.map),this.nearestMarkers.push(m)}})}calculateRoutes(t){return f(this,null,function*(){if(this.userLocation){this.routeLayer=g.layerGroup().addTo(this.map);for(let n=0;n<t.length;n++){let o=t[n],e=Number(o.latitude),u=Number(o.longitude);if(!isNaN(e)&&!isNaN(u))try{let d=this.mapboxRouting.convertTravelModeToProfile(this.travelMode),l=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,u,e,d);if(l&&l.routes&&l.routes.length>0){let p=l.routes[0],m="#3880ff";o.disaster_type==="Earthquake"?m="#ff9500":o.disaster_type==="Typhoon"?m="#2dd36f":o.disaster_type==="Flash Flood"&&(m="#3dc2ff"),g.polyline(p.geometry.coordinates.map(b=>[b[1],b[0]]),{color:m,weight:4,opacity:.8,dashArray:n===0?void 0:"10, 10"}).addTo(this.routeLayer),n===0&&(this.routeTime=p.duration,this.routeDistance=p.distance),console.log(`\u{1F5FA}\uFE0F Route ${n+1}: ${(p.distance/1e3).toFixed(2)}km, ${(p.duration/60).toFixed(0)}min`)}}catch(d){console.error(`\u{1F5FA}\uFE0F Error calculating route to center ${n+1}:`,d)}}}})}clearRoutes(){this.routeLayer&&(this.map.removeLayer(this.routeLayer),this.routeLayer=null),this.nearestMarkers.forEach(t=>{this.map.removeLayer(t)}),this.nearestMarkers=[],this.map.eachLayer(t=>{(t instanceof g.GeoJSON||t instanceof g.Polyline||t.options&&(t.options.color||t.isRouteLayer))&&(["#ff9500","#2dd36f","#3dc2ff","#3880ff","#008000","#0066CC","#ef4444","#dc3545","#ffa500","#17a2b8","#007bff"].includes(t.options.color)||t.isRouteLayer||t.isNavigationRoute)&&this.map.removeLayer(t)}),this.routeTime=0,this.routeDistance=0}changeTravelMode(t){return f(this,null,function*(){this.travelMode=t,yield(yield this.toastCtrl.create({message:`\u{1F6B6}\u200D\u2642\uFE0F Travel mode changed to ${t}`,duration:2e3,color:"primary"})).present(),this.userLocation&&this.evacuationCenters.length>0&&(yield this.routeToTwoNearestCenters())})}showNavigationPanel(t){return f(this,null,function*(){this.selectedCenter=t,this.selectedTransportMode=null,this.routeInfo={},yield this.calculateAllRoutes(t)})}closeNavigationPanel(){this.selectedCenter=null,this.selectedTransportMode=null,this.routeInfo={}}selectTransportMode(t){return f(this,null,function*(){this.selectedTransportMode=t,this.selectedCenter&&this.routeInfo[t]&&(yield this.routeToCenter(this.selectedCenter,t))})}calculateAllRoutes(t){return f(this,null,function*(){if(!this.userLocation)return;let n=Number(t.latitude),o=Number(t.longitude);if(isNaN(n)||isNaN(o))return;let e=["walking","cycling","driving"];for(let u of e)try{let d=this.mapboxRouting.convertTravelModeToProfile(u),l=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,o,n,d);if(l&&l.routes&&l.routes.length>0){let p=l.routes[0];this.routeInfo[u]={duration:p.duration,distance:p.distance}}}catch(d){console.error(`Error calculating ${u} route:`,d)}})}formatTime(t){return t?`${Math.round(t/60)} min`:"--"}formatDistance(t){if(!t)return"--";let n=t/1e3;return n<1?`${Math.round(t)} m`:`${n.toFixed(1)} km`}startNavigation(){return f(this,null,function*(){if(!this.selectedCenter||!this.selectedTransportMode)return;yield this.routeToCenter(this.selectedCenter,this.selectedTransportMode),this.closeNavigationPanel(),yield(yield this.toastCtrl.create({message:`\u{1F9ED} Navigation started to ${this.selectedCenter.name}`,duration:3e3,color:"success",position:"top"})).present()})}routeToCenter(t,n){return f(this,null,function*(){if(this.userLocation)try{this.clearRoutes();let o=Number(t.latitude),e=Number(t.longitude);if(!isNaN(o)&&!isNaN(e)){let u=this.mapboxRouting.convertTravelModeToProfile(n),d=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,e,o,u);if(d&&d.routes&&d.routes.length>0){let l=d.routes[0],p="#3880ff",m="\u{1F535}";t.disaster_type==="Earthquake"?(p="#ff9500",m="\u{1F7E0}"):t.disaster_type==="Typhoon"?(p="#2dd36f",m="\u{1F7E2}"):t.disaster_type==="Flash Flood"&&(p="#3dc2ff",m="\u{1F535}"),this.routeLayer=g.layerGroup().addTo(this.map);let y=g.polyline(l.geometry.coordinates.map(L=>[L[1],L[0]]),{color:p,weight:5,opacity:.8});y.isRouteLayer=!0,y.addTo(this.routeLayer),yield(yield this.toastCtrl.create({message:`${m} Route: ${(l.distance/1e3).toFixed(2)}km, ${(l.duration/60).toFixed(0)}min via ${n}`,duration:4e3,color:"primary"})).present(),this.map.fitBounds(y.getBounds(),{padding:[50,50]})}}}catch(o){console.error("\u{1F5FA}\uFE0F Error routing to center:",o),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}goBack(){this.router.navigate(["/tabs/home"])}showAllCenters(){this.showAllCentersPanel=!0,this.selectedCenter=null}closeAllCentersPanel(){this.showAllCentersPanel=!1}selectCenterFromList(t){this.closeAllCentersPanel(),this.showNavigationPanel(t)}calculateDistanceInKm(t){return this.userLocation?(this.calculateDistance(this.userLocation.lat,this.userLocation.lng,Number(t.latitude),Number(t.longitude))/1e3).toFixed(1):"N/A"}downloadMap(){return f(this,null,function*(){if(!this.map){yield(yield this.toastCtrl.create({message:"Map not loaded yet. Please wait and try again.",duration:3e3,color:"warning"})).present();return}try{yield this.enhancedDownload.downloadMapWithRoutes("all-maps",this.map,"All-Evacuation-Centers",!0)}catch(t){console.error("Enhanced download error:",t),yield(yield this.toastCtrl.create({message:"Failed to download map. Please try again.",duration:3e3,color:"danger"})).present()}})}ionViewWillLeave(){this.clearRoutes(),this.isRealTimeNavigationActive&&this.osmRouting.stopRealTimeRouting(),this.map&&this.map.remove()}startRealTimeNavigation(t){console.log("\u{1F9ED} Starting real-time navigation to center:",t.name),this.navigationDestination={lat:Number(t.latitude),lng:Number(t.longitude),name:t.name},this.isRealTimeNavigationActive=!0,this.toastCtrl.create({message:`\u{1F9ED} Real-time navigation started to ${t.name}`,duration:3e3,color:"primary"}).then(n=>n.present())}onNavigationRouteUpdated(t){console.log("\u{1F504} All maps navigation route updated"),this.currentNavigationRoute=t,this.updateMapWithNavigationRoute(t)}onNavigationStopped(){console.log("\u23F9\uFE0F All maps real-time navigation stopped"),this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.clearNavigationRoute(),this.toastCtrl.create({message:"\u23F9\uFE0F Navigation stopped",duration:2e3,color:"medium"}).then(t=>t.present())}updateMapWithNavigationRoute(t){if(this.clearNavigationRoute(),t.geometry&&t.geometry.coordinates){let n=this.osmRouting.convertToGeoJSON(t),o=g.geoJSON(n,{style:{color:"#007bff",weight:6,opacity:.8,dashArray:"10, 5"}}).addTo(this.map);o.isNavigationRoute=!0}}clearNavigationRoute(){this.map.eachLayer(t=>{t.isNavigationRoute&&this.map.removeLayer(t)})}static{this.\u0275fac=function(n){return new(n||c)}}static{this.\u0275cmp=R({type:c,selectors:[["app-all-maps"]],standalone:!0,features:[F],decls:80,vars:20,consts:[[3,"translucent"],["color","secondary"],[3,"fullscreen"],["id","all-maps",2,"height","100%","width","100%"],[1,"map-controls"],["fill","clear",1,"control-btn",3,"click"],["src","assets/ListOfEvacuationArea.png","alt","All Centers",1,"control-icon"],["src","assets/downloadForSeeWholeMap.png","alt","Download",1,"control-icon"],[3,"destination","travelMode","autoStart","routeUpdated","navigationStopped",4,"ngIf"],[1,"all-centers-panel"],[1,"panel-content"],[1,"panel-header"],["fill","clear",1,"back-btn",3,"click"],["src","assets/backIcon.png","alt","Back",1,"back-icon"],[1,"header-info"],[1,"disaster-counts"],[1,"count-row"],[1,"disaster-icon"],[1,"disaster-label"],[1,"disaster-count"],[1,"centers-list"],["class","center-item",3,"click",4,"ngFor","ngForOf"],[1,"all-centers-overlay",3,"click"],[1,"navigation-panel"],["class","center-info",4,"ngIf"],["fill","clear","size","small",3,"click"],["name","close-outline"],["class","transport-options",4,"ngIf"],[3,"routeUpdated","navigationStopped","destination","travelMode","autoStart"],[1,"center-item",3,"click"],[1,"center-info"],[1,"address"],[1,"center-details"],["class","distance",4,"ngIf"],[1,"capacity"],[1,"center-actions"],["name","chevron-forward-outline"],[1,"distance"],[1,"transport-options"],[1,"option-header"],["name","navigate-outline"],[1,"transport-buttons"],[1,"transport-btn",3,"click"],["name","walk-outline"],["class","route-info",4,"ngIf"],["name","bicycle-outline"],["name","car-outline"],["class","start-navigation-btn",3,"click",4,"ngIf"],[1,"route-info"],[1,"time"],[1,"start-navigation-btn",3,"click"],["name","navigate"]],template:function(n,o){n&1&&(i(0,"ion-header",0),v(1,"ion-toolbar",1),a(),i(2,"ion-content",2),v(3,"div",3),i(4,"div",4)(5,"ion-button",5),M("click",function(){return o.showAllCenters()}),v(6,"img",6),a(),i(7,"ion-button",5),M("click",function(){return o.downloadMap()}),v(8,"img",7),a()(),P(9,lt,1,3,"app-real-time-navigation",8),i(10,"div",9)(11,"div",10)(12,"div",11)(13,"ion-button",12),M("click",function(){return o.closeAllCentersPanel()}),v(14,"img",13),a(),i(15,"div",14)(16,"h3"),r(17,"\u{1F5FA}\uFE0F All Evacuation Centers"),a(),i(18,"p"),r(19),a()()(),i(20,"div",15)(21,"div",16)(22,"span",17),r(23,"\u{1F7E0}"),a(),i(24,"span",18),r(25,"Earthquake:"),a(),i(26,"span",19),r(27),a()(),i(28,"div",16)(29,"span",17),r(30,"\u{1F7E2}"),a(),i(31,"span",18),r(32,"Typhoon:"),a(),i(33,"span",19),r(34),a()(),i(35,"div",16)(36,"span",17),r(37,"\u{1F535}"),a(),i(38,"span",18),r(39,"Flood:"),a(),i(40,"span",19),r(41),a()(),i(42,"div",16)(43,"span",17),r(44,"\u{1F534}"),a(),i(45,"span",18),r(46,"Fire:"),a(),i(47,"span",19),r(48),a()(),i(49,"div",16)(50,"span",17),r(51,"\u{1F7E4}"),a(),i(52,"span",18),r(53,"Landslide:"),a(),i(54,"span",19),r(55),a()(),i(56,"div",16)(57,"span",17),r(58,"\u{1F7E3}"),a(),i(59,"span",18),r(60,"Others:"),a(),i(61,"span",19),r(62),a()(),i(63,"div",16)(64,"span",17),r(65,"\u{1F518}"),a(),i(66,"span",18),r(67,"Multiple:"),a(),i(68,"span",19),r(69),a()()(),i(70,"div",20),P(71,pt,12,4,"div",21),a()()(),i(72,"div",22),M("click",function(){return o.closeAllCentersPanel()}),a(),i(73,"div",23)(74,"div",10)(75,"div",11),P(76,dt,5,2,"div",24),i(77,"ion-button",25),M("click",function(){return o.closeNavigationPanel()}),v(78,"ion-icon",26),a()(),P(79,_t,22,10,"div",27),a()()()),n&2&&(C("translucent",!0),s(2),C("fullscreen",!0),s(7),C("ngIf",o.navigationDestination),s(),A("show",o.showAllCentersPanel),s(9),S("",o.centerCounts.total," centers available"),s(8),h(o.centerCounts.earthquake),s(7),h(o.centerCounts.typhoon),s(7),h(o.centerCounts.flood),s(7),h(o.centerCounts.fire),s(7),h(o.centerCounts.landslide),s(7),h(o.centerCounts.others),s(7),h(o.centerCounts.multiple),s(2),C("ngForOf",o.evacuationCenters),s(),A("show",o.showAllCentersPanel),s(),A("show",o.selectedCenter),s(3),C("ngIf",o.selectedCenter),s(3),C("ngIf",o.selectedCenter))},dependencies:[X,j,G,B,J,H,V,z,$,W,it],styles:["#all-maps[_ngcontent-%COMP%]{height:100%;width:100%;z-index:1}.map-controls[_ngcontent-%COMP%]{position:absolute;top:95px;right:15px;z-index:1000;display:flex;flex-direction:column;gap:8px}.control-btn[_ngcontent-%COMP%]{--background: rgba(255, 255, 255, .95);--color: var(--ion-color-primary);--border-radius: 12px;width:48px;height:48px;box-shadow:0 2px 12px #00000026;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.control-btn[_ngcontent-%COMP%]   .control-icon[_ngcontent-%COMP%]{width:24px;height:24px;object-fit:contain}.all-centers-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-100%;width:350px;height:100vh;background:#fff;z-index:2000;transition:right .3s ease-in-out;box-shadow:-4px 0 20px #00000026}.all-centers-panel.show[_ngcontent-%COMP%]{right:0}.all-centers-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;padding:60px 20px 20px}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:20px;padding-bottom:16px;border-bottom:1px solid var(--ion-color-light);gap:12px}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]{--background: transparent;--color: var(--ion-color-medium);margin:0;padding:8px;min-width:auto;width:auto;height:auto}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%]{width:24px;height:24px;object-fit:contain}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]{flex:1}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:18px;font-weight:600;color:var(--ion-color-secondary);line-height:1.2}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);line-height:1.3}.all-centers-panel[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]{margin-bottom:20px;padding:16px;background:var(--ion-color-light);border-radius:12px}.all-centers-panel[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin:6px 0;font-size:14px}.all-centers-panel[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]   .disaster-icon[_ngcontent-%COMP%]{font-size:16px;width:20px;text-align:center}.all-centers-panel[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]   .disaster-label[_ngcontent-%COMP%]{flex:1;color:var(--ion-color-dark);font-weight:500}.all-centers-panel[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]   .disaster-count[_ngcontent-%COMP%]{font-weight:600;color:var(--ion-color-secondary);min-width:24px;text-align:right}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]{flex:1;overflow-y:auto}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;margin-bottom:8px;background:#fff;border:1px solid var(--ion-color-light);border-radius:12px;cursor:pointer;transition:all .2s ease}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]:hover{background:var(--ion-color-light);border-color:var(--ion-color-primary-tint)}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]{flex:1}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 4px;font-size:16px;font-weight:600;color:var(--ion-color-dark);line-height:1.2}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .address[_ngcontent-%COMP%]{margin:0 0 8px;font-size:13px;color:var(--ion-color-medium);line-height:1.3}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:2px}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%], .all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .capacity[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium)}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{color:var(--ion-color-primary);font-weight:500}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-actions[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:var(--ion-color-medium)}.all-centers-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#0000004d;z-index:1999;opacity:0;visibility:hidden;transition:all .3s ease}.all-centers-overlay.show[_ngcontent-%COMP%]{opacity:1;visibility:visible}ion-toolbar[_ngcontent-%COMP%]{--background: var(--ion-color-secondary);--color: white}ion-title[_ngcontent-%COMP%]{font-weight:600}.transport-controls[_ngcontent-%COMP%]{position:absolute;bottom:120px;left:20px;z-index:1000;max-width:280px}.transport-controls[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.transport-controls[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.transport-controls[_ngcontent-%COMP%]   .transport-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-primary);margin-bottom:8px}.transport-controls[_ngcontent-%COMP%]   .transport-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.transport-controls[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]{--background: rgba(var(--ion-color-light-rgb), .3);border-radius:8px}.transport-controls[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);--color-checked: var(--ion-color-primary);--indicator-color: var(--ion-color-primary);min-height:40px}.transport-controls[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;margin-bottom:2px}.transport-controls[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:12px;font-weight:500}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse-container[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center;justify-content:center}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse[_ngcontent-%COMP%]{position:absolute;width:60px;height:60px;border-radius:50%;opacity:.6;animation:_ngcontent-%COMP%_pulse 2s infinite;z-index:1}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-icon[_ngcontent-%COMP%]{width:40px;height:40px;z-index:2;position:relative}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-label[_ngcontent-%COMP%]{position:absolute;top:-8px;right:-8px;background:var(--ion-color-primary);color:#fff;border-radius:50%;width:20px;height:20px;display:flex;align-items:center;justify-content:center;font-size:12px;font-weight:700;z-index:3;border:2px solid white}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(.8);opacity:.8}50%{transform:scale(1.2);opacity:.4}to{transform:scale(.8);opacity:.8}}.navigation-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-100%;width:320px;height:100vh;background:#fff;z-index:2000;transition:right .3s ease-in-out;box-shadow:-4px 0 20px #00000026}.navigation-panel.show[_ngcontent-%COMP%]{right:0}.navigation-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;padding:60px 20px 20px}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:24px;padding-bottom:16px;border-bottom:1px solid var(--ion-color-light)}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:18px;font-weight:600;color:var(--ion-color-dark);line-height:1.2}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);line-height:1.3}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);margin:0}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .option-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:16px;font-weight:600;color:var(--ion-color-primary)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .option-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-buttons[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;margin-bottom:24px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;border:2px solid var(--ion-color-light);border-radius:12px;background:#fff;cursor:pointer;transition:all .2s ease;position:relative}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]:hover{border-color:var(--ion-color-primary-tint);background:var(--ion-color-primary-tint)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn.active[_ngcontent-%COMP%]{border-color:var(--ion-color-primary);background:var(--ion-color-primary-tint)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:var(--ion-color-primary)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-right:12px;color:var(--ion-color-medium);transition:color .2s ease}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:var(--ion-color-dark);flex:1}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-end;gap:2px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:var(--ion-color-primary)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]{width:100%;padding:16px;background:var(--ion-color-primary);color:#fff;border:none;border-radius:12px;font-size:16px;font-weight:600;display:flex;align-items:center;justify-content:center;gap:8px;cursor:pointer;transition:background .2s ease}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]:hover{background:var(--ion-color-primary-shade)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]{text-align:center;min-width:200px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-secondary);font-size:16px;font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:4px 0;color:var(--ion-color-dark);font-size:14px;font-weight:500}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;font-size:14px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-dark)}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup.nearest-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--ion-color-success);font-size:18px}"]})}}return c})();export{It as AllMapsPage};
